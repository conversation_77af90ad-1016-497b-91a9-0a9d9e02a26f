# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import flt


class NepseTransaction(Document):
	def validate(self):
		"""Validate transaction data"""
		self.calculate_amounts()
		self.validate_transaction()
	
	def calculate_amounts(self):
		"""Calculate total and net amounts"""
		self.total_amount = flt(self.quantity) * flt(self.price_per_share)
		
		if self.transaction_type == "Buy":
			self.net_amount = flt(self.total_amount) + flt(self.brokerage)
		else:  # Sell
			self.net_amount = flt(self.total_amount) - flt(self.brokerage)
	
	def validate_transaction(self):
		"""Validate transaction logic"""
		if flt(self.quantity) <= 0:
			frappe.throw("Quantity must be greater than 0")
		
		if flt(self.price_per_share) <= 0:
			frappe.throw("Price per share must be greater than 0")
		
		# For sell transactions, check if user has enough shares
		if self.transaction_type == "Sell":
			self.validate_sell_quantity()
	
	def validate_sell_quantity(self):
		"""Validate if user has enough shares to sell"""
		current_holdings = get_current_holdings(self.symbol)

		if flt(current_holdings) < flt(self.quantity):
			frappe.throw(f"Insufficient shares. Current holdings: {current_holdings}, Trying to sell: {self.quantity}")

	def on_submit(self):
		"""Mark transaction as completed when submitted"""
		self.status = "Completed"

	def on_cancel(self):
		"""Mark transaction as cancelled when cancelled"""
		self.status = "Cancelled"


def get_current_holdings(symbol):
	"""Get current holdings for a symbol by calculating from transactions"""
	# Get all submitted buy transactions
	buy_qty = frappe.db.sql("""
		SELECT SUM(quantity)
		FROM `tabNepse Transaction`
		WHERE symbol = %s AND transaction_type = 'Buy' AND docstatus = 1
	""", symbol)[0][0] or 0

	# Get all submitted sell transactions
	sell_qty = frappe.db.sql("""
		SELECT SUM(quantity)
		FROM `tabNepse Transaction`
		WHERE symbol = %s AND transaction_type = 'Sell' AND docstatus = 1
	""", symbol)[0][0] or 0

	return flt(buy_qty) - flt(sell_qty)



