# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.utils import flt, getdate
from frappe import _


def execute(filters=None):
	"""Execute the Nepse Profit Loss Report"""
	columns = get_columns()
	data = get_data(filters)
	
	return columns, data


def get_columns():
	"""Define report columns"""
	return [
		{
			"fieldname": "symbol",
			"label": _("Symbol"),
			"fieldtype": "Link",
			"options": "Nepse Symbol",
			"width": 100
		},
		{
			"fieldname": "company_name",
			"label": _("Company Name"),
			"fieldtype": "Data",
			"width": 200
		},
		{
			"fieldname": "quantity",
			"label": _("Quantity"),
			"fieldtype": "Int",
			"width": 100
		},
		{
			"fieldname": "average_cost",
			"label": _("Avg Cost"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "current_price",
			"label": _("Current Price"),
			"fieldtype": "Currency",
			"width": 120
		},
		{
			"fieldname": "total_invested",
			"label": _("Total Invested"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "current_value",
			"label": _("Current Value"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "unrealized_gain_loss",
			"label": _("Unrealized P&L"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "realized_gain_loss",
			"label": _("Realized P&L"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "total_gain_loss",
			"label": _("Total P&L"),
			"fieldtype": "Currency",
			"width": 130
		},
		{
			"fieldname": "gain_loss_percentage",
			"label": _("P&L %"),
			"fieldtype": "Percent",
			"width": 100
		},
		{
			"fieldname": "sector",
			"label": _("Sector"),
			"fieldtype": "Data",
			"width": 120
		}
	]


def get_data(filters):
	"""Get report data calculated from transactions"""
	# Get all symbols that have transactions
	symbols_query = """
		SELECT DISTINCT t.symbol
		FROM `tabNepse Transaction` t
		WHERE t.docstatus = 1
	"""

	if filters.get("symbol"):
		symbols_query += " AND t.symbol = %(symbol)s"

	symbols = frappe.db.sql(symbols_query, filters, as_dict=1)

	data = []
	for row in symbols:
		symbol = row.symbol
		portfolio_data = calculate_portfolio_metrics(symbol, filters)

		# Only include if there are current holdings or if not filtering for holdings only
		if portfolio_data['quantity'] > 0 or not filters.get('show_only_holdings'):
			data.append(portfolio_data)

	# Sort by total gain/loss descending
	data.sort(key=lambda x: x['total_gain_loss'], reverse=True)

	return data


def calculate_portfolio_metrics(symbol, filters=None):
	"""Calculate portfolio metrics for a symbol from transactions"""
	# Get symbol information
	symbol_info = frappe.db.get_value("Nepse Symbol", symbol,
		["company_name", "sector", "last_traded_price"], as_dict=1) or {}

	# Calculate current holdings
	buy_qty = frappe.db.sql("""
		SELECT SUM(quantity), SUM(net_amount)
		FROM `tabNepse Transaction`
		WHERE symbol = %s AND transaction_type = 'Buy' AND docstatus = 1
	""", symbol)[0]

	sell_qty = frappe.db.sql("""
		SELECT SUM(quantity), SUM(net_amount)
		FROM `tabNepse Transaction`
		WHERE symbol = %s AND transaction_type = 'Sell' AND docstatus = 1
	""", symbol)[0]

	total_buy_qty = flt(buy_qty[0]) if buy_qty[0] else 0
	total_buy_amount = flt(buy_qty[1]) if buy_qty[1] else 0
	total_sell_qty = flt(sell_qty[0]) if sell_qty[0] else 0
	total_sell_amount = flt(sell_qty[1]) if sell_qty[1] else 0

	current_quantity = total_buy_qty - total_sell_qty

	# Calculate average cost (only for remaining shares)
	if current_quantity > 0 and total_buy_qty > 0:
		# Weighted average cost of remaining shares
		average_cost = total_buy_amount / total_buy_qty
	else:
		average_cost = 0

	# Get current price
	current_price = flt(symbol_info.get('last_traded_price', 0))

	# Calculate values
	total_invested = current_quantity * average_cost
	current_value = current_quantity * current_price
	unrealized_gain_loss = current_value - total_invested

	# Calculate realized gain/loss (simplified: sell amount - cost of sold shares)
	if total_sell_qty > 0:
		cost_of_sold_shares = total_sell_qty * average_cost
		realized_gain_loss = total_sell_amount - cost_of_sold_shares
	else:
		realized_gain_loss = 0

	total_gain_loss = unrealized_gain_loss + realized_gain_loss

	# Calculate percentage
	if total_invested > 0:
		gain_loss_percentage = (total_gain_loss / total_invested) * 100
	else:
		gain_loss_percentage = 0

	return {
		'symbol': symbol,
		'company_name': symbol_info.get('company_name', ''),
		'sector': symbol_info.get('sector', ''),
		'quantity': current_quantity,
		'average_cost': average_cost,
		'current_price': current_price,
		'total_invested': total_invested,
		'current_value': current_value,
		'unrealized_gain_loss': unrealized_gain_loss,
		'realized_gain_loss': realized_gain_loss,
		'total_gain_loss': total_gain_loss,
		'gain_loss_percentage': gain_loss_percentage
	}


@frappe.whitelist()
def get_portfolio_summary(filters=None):
	"""Get overall portfolio summary calculated from transactions"""
	data = get_data(filters or {})

	summary = {
		'total_invested': 0,
		'current_value': 0,
		'total_unrealized_gain_loss': 0,
		'total_realized_gain_loss': 0,
		'total_gain_loss': 0,
		'portfolio_count': len([d for d in data if d['quantity'] > 0])
	}

	for row in data:
		if row['quantity'] > 0:  # Only count current holdings
			summary['total_invested'] += flt(row['total_invested'])
			summary['current_value'] += flt(row['current_value'])
			summary['total_unrealized_gain_loss'] += flt(row['unrealized_gain_loss'])
			summary['total_realized_gain_loss'] += flt(row['realized_gain_loss'])
			summary['total_gain_loss'] += flt(row['total_gain_loss'])

	# Calculate overall percentage
	if summary['total_invested'] > 0:
		summary['total_gain_loss_percentage'] = (summary['total_gain_loss'] / summary['total_invested']) * 100
	else:
		summary['total_gain_loss_percentage'] = 0

	return summary
