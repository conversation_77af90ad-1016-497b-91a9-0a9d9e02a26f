# Copyright (c) 2025, <PERSON><PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests.utils import FrappeTestCase


class TestNepseSymbol(FrappeTestCase):
	def test_symbol_creation(self):
		"""Test creating a new symbol"""
		symbol = frappe.get_doc({
			"doctype": "Nepse Symbol",
			"symbol": "TEST",
			"company_name": "Test Company Ltd.",
			"sector": "Banking",
			"status": "Active"
		})
		symbol.insert()
		
		# Check if symbol is created and auto-named correctly
		self.assertEqual(symbol.name, "TEST")
		self.assertEqual(symbol.symbol, "TEST")
		
		# Clean up
		symbol.delete()
	
	def test_symbol_validation(self):
		"""Test symbol validation (uppercase conversion)"""
		symbol = frappe.get_doc({
			"doctype": "Nepse Symbol",
			"symbol": "test",
			"company_name": "Test Company Ltd.",
			"sector": "Banking",
			"status": "Active"
		})
		symbol.insert()
		
		# Check if symbol is converted to uppercase
		self.assertEqual(symbol.symbol, "TEST")
		
		# Clean up
		symbol.delete()
