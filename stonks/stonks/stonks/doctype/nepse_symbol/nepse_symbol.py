# Copyright (c) 2025, <PERSON><PERSON><PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe.utils import now


class NepseSymbol(Document):
	def validate(self):
		"""Validate the symbol data"""
		if self.symbol:
			self.symbol = self.symbol.upper()

		# Update last_updated timestamp when price is updated
		if self.has_value_changed("last_traded_price"):
			self.last_updated = now()
