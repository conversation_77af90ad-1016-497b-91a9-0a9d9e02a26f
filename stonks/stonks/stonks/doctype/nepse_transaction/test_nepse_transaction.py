# Copyright (c) 2025, <PERSON><PERSON><PERSON> and Contributors
# See license.txt

import frappe
from frappe.tests.utils import FrappeTestCase


class TestNepseTransaction(FrappeTestCase):
	def setUp(self):
		"""Set up test data"""
		# Create test symbol
		if not frappe.db.exists("Nepse Symbol", "TEST"):
			symbol = frappe.get_doc({
				"doctype": "Nepse Symbol",
				"symbol": "TEST",
				"company_name": "Test Company Ltd.",
				"sector": "Banking",
				"status": "Active"
			})
			symbol.insert()
	
	def test_buy_transaction(self):
		"""Test buy transaction creation and calculation"""
		transaction = frappe.get_doc({
			"doctype": "Nepse Transaction",
			"transaction_type": "Buy",
			"symbol": "TEST",
			"transaction_date": "2025-06-22",
			"quantity": 100,
			"price_per_share": 500,
			"brokerage": 250
		})
		transaction.insert()
		
		# Check calculations
		self.assertEqual(transaction.total_amount, 50000)  # 100 * 500
		self.assertEqual(transaction.net_amount, 50250)    # 50000 + 250
		
		# Clean up
		transaction.delete()
	
	def test_sell_transaction_calculation(self):
		"""Test sell transaction calculation"""
		transaction = frappe.get_doc({
			"doctype": "Nepse Transaction",
			"transaction_type": "Sell",
			"symbol": "TEST",
			"transaction_date": "2025-06-22",
			"quantity": 50,
			"price_per_share": 600,
			"brokerage": 150
		})
		transaction.insert()
		
		# Check calculations
		self.assertEqual(transaction.total_amount, 30000)  # 50 * 600
		self.assertEqual(transaction.net_amount, 29850)    # 30000 - 150
		
		# Clean up
		transaction.delete()
	
	def tearDown(self):
		"""Clean up test data"""
		# Clean up test symbol
		if frappe.db.exists("Nepse Symbol", "TEST"):
			frappe.delete_doc("Nepse Symbol", "TEST")
