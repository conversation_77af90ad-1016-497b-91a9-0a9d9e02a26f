# import pandas as pd
import frappe
import click
from frappe.custom.doctype.custom_field.custom_field import create_custom_field
from frappe.commands import pass_context

# @click.command('migrate_chart_of_accounts')
# @click.argument('site')
# def migrate_chart_of_accounts(site):
#     try:
#         frappe.init(site=site)
#         frappe.connect()

#         df = pd.read_excel("/home/<USER>/Downloads/map_coa_file (1).xlsx")

#         for _, row in df.iterrows():
#             old_account_name = row["Old"]
#             new_account_name = row["New"]
#             new_parent_name = row["Parent name"]
#             company_name = row["Company"]

#             try:
#                 old_account = frappe.get_doc("Account", {
#                     "account_name": old_account_name,
#                     "company": company_name
#                 })
#             except frappe.DoesNotExistError:
#                 print(f"❌ Old account not found: {old_account_name} ({company_name})")
#                 continue

#             old_account.parent_account = new_parent_name
#             old_account.save()
#             frappe.db.commit()
#             print(f"✅ Set parent of {old_account_name} → {new_parent_name} [{company_name}]")

#             company_abbr = frappe.get_value("Company", company_name, "abbr")
#             new_full_account_name = f"{new_account_name} - {company_abbr}"

#             # 🆕 If old and new names are the same
#             if old_account_name == new_account_name:
#                 print(f"⚠️ name already exist: {old_account_name} is the same as new name in {company_name}")
#                 # try:
#                 #     new_account = frappe.get_doc("Account", {
#                 #         "account_name": new_account_name,
#                 #         "company": company_name
#                 #     })
#                 #     if new_account.name != old_account.name:
#                 #         try:
#                 #             new_account.delete(ignore_permissions=True, force=True)
#                 #             frappe.db.commit()
#                 #             print(f"🗑️ Deleted conflicting account with same name: {new_account_name} [{company_name}]")
#                 #         except Exception as delete_error:
#                 #             if "disable this Account" in str(delete_error):
#                 #                 new_account.disabled = 1
#                 #                 new_account.save()
#                 #                 frappe.db.commit()
#                 #                 print(f"🚫 Disabled instead of deleting: {new_account_name} [{company_name}]")
#                 #             else:
#                 #                 print(f"❌ Delete failed: {new_account_name} [{company_name}]: {str(delete_error)}")
#                 # except frappe.DoesNotExistError:
#                 #     print(f"✅ No conflicting account with same name: {new_account_name} in {company_name}")

#             else:
#                 # If names are different, delete new if it exists
#                 try:
#                     new_account = frappe.get_doc("Account", {
#                         "account_name": new_account_name,
#                         "company": company_name
#                     })
#                     try:
#                         new_account.delete(ignore_permissions=True, force=True)
#                         frappe.db.commit()
#                         print(f"🗑️ Deleted existing account: {new_account_name} [{company_name}]")
#                     except Exception as delete_error:
#                         if "disable this Account" in str(delete_error):
#                             new_account.disabled = 1
#                             new_account.save()
#                             frappe.db.commit()
#                             print(f"🚫 Disabled instead of deleting: {new_account_name} [{company_name}]")
#                         else:
#                             print(f"❌ Delete failed: {new_account_name} [{company_name}]: {str(delete_error)}")
#                 except frappe.DoesNotExistError:
#                     print(f"✅ No existing account named {new_account_name} in {company_name}")

#             # Rename step
#             try:
#                 if frappe.db.exists("Account", new_full_account_name):
#                     print(f"⚠️ Skip rename: {new_full_account_name} already exists in {company_name}")
#                 else:
#                     frappe.rename_doc("Account", old_account.name, new_full_account_name, force=True)
#                     frappe.db.commit()
#                     print(f"🔁 Renamed {old_account.name} → {new_full_account_name} [{company_name}]")
#             except Exception as e:
#                 print(f"❌ Rename failed: {old_account_name} → {new_full_account_name} [{company_name}]: {str(e)}")

#     except Exception as e:
#         print(f"❌ Error processing file: {str(e)}")


@click.command("update-salary-slip-fields")
@click.argument("site")
def update_salary_slip_fields(site):
    try:
        frappe.init(site=site)
        frappe.connect()
        frappe.logger().info("Connected to site.")

        doctype = "Salary Slip"
        fieldname = "hourly_rate"
        label = "Hourly Rate"
        insert_after = "payroll_entry"

        if not frappe.db.exists("Custom Field", f"{doctype}-{fieldname}"):
            create_custom_field(
                doctype,
                {
                    "fieldname": fieldname,
                    "label": label,
                    "fieldtype": "Float",
                    "insert_after": insert_after,
                    "read_only": 1,
                },
            )
            click.echo(
                f"Added read-only custom field '{label}' after '{insert_after}'."
            )
        else:
            click.echo(f"Field '{fieldname}' already exists in '{doctype}'.")

        frappe.db.commit()
        frappe.clear_cache(doctype=doctype)
        click.echo("Changes applied and cache cleared.")

    except Exception as e:
        click.echo(f"Error: {str(e)}")

    finally:
        frappe.destroy()


@click.command("create-overtime-field")
@click.argument("site")
def create_overtime_field(site):
    try:
        frappe.init(site=site)
        frappe.connect()
        frappe.logger().info("Connected to site.")

        doctype = "Salary Slip"
        fieldname = "overtime_hours"
        label = "Overtime Hours"
        insert_after = "hourly_rate"

        if not frappe.db.exists("Custom Field", f"{doctype}-{fieldname}"):
            create_custom_field(
                doctype,
                {
                    "fieldname": fieldname,
                    "label": label,
                    "fieldtype": "Float",
                    "insert_after": insert_after,
                    "read_only": 1,
                    "default": "0",
                    "print_hide": 0,
                },
            )
            click.echo(
                f"✅ Added read-only custom field '{label}' after '{insert_after}'."
            )
        else:
            click.echo(f"⚠️ Field '{fieldname}' already exists in '{doctype}'.")

        frappe.db.commit()
        frappe.clear_cache(doctype=doctype)
        click.echo("🔄 Changes applied and cache cleared.")

    except Exception as e:
        click.echo(f"❌ Error: {str(e)}")

    finally:
        frappe.destroy()


# Register command
commands = [
    # migrate_chart_of_accounts,
    update_salary_slip_fields,
    create_overtime_field,
]
