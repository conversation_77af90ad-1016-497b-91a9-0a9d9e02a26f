{"actions": [], "allow_rename": 1, "autoname": "field:symbol", "creation": "2025-06-22 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["symbol", "company_name", "sector", "column_break_4", "listing_date", "status", "section_break_7", "market_capitalization", "shares_outstanding", "column_break_10", "last_traded_price", "last_updated"], "fields": [{"fieldname": "symbol", "fieldtype": "Data", "in_list_view": 1, "label": "Symbol", "reqd": 1, "unique": 1}, {"fieldname": "company_name", "fieldtype": "Data", "in_list_view": 1, "label": "Company Name", "reqd": 1}, {"fieldname": "sector", "fieldtype": "Select", "in_list_view": 1, "label": "Sector", "options": "Banking\nInsurance\nHydropower\nManufacturing\nHotels and Tourism\nTrading\nFinance\nDevelopment Bank\nMicrofinance\nMutual Fund\nOthers"}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "listing_date", "fieldtype": "Date", "label": "Listing Date"}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Active\nSuspended\nDelisted", "default": "Active"}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Market Information"}, {"fieldname": "market_capitalization", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Market Capitalization"}, {"fieldname": "shares_outstanding", "fieldtype": "Int", "label": "Shares Outstanding"}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "last_traded_price", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Last Traded Price"}, {"fieldname": "last_updated", "fieldtype": "Datetime", "label": "Last Updated"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-06-22 10:00:00.000000", "modified_by": "Administrator", "module": "Stonks", "name": "Nepse Symbol", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}