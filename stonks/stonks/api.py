from nepse import Nepse
import frappe


def find_multiple_data_by_key(data, key, values):
    """Find multiple data items by key-value pairs efficiently"""
    results = {}
    values_set = set(values)

    if data and isinstance(data, list):
        for item in data:
            item_value = item.get(key)
            if item_value in values_set:
                results[item_value] = item
                values_set.discard(item_value)
                if not values_set:
                    break

    return results


def get_prices_history(symbols, date):
    """Get historical price data for multiple symbols efficiently"""
    nepse = Nepse()
    nepse.setTLSVerification(False)
    data = nepse.getPriceVolumeHistory(business_date=date)
    data = data['content']

    results = find_multiple_data_by_key(data, 'symbol', symbols)
    return results



@frappe.whitelist()
def sync_symbols_from_api():
	"""Sync symbols from API to Nepse Symbol doctype"""
	try:
		# Get company list from API
		nepse = Nepse()
		nepse.setTLSVerification(False)
		company_data = nepse.getCompanyList()

		if not company_data:
			return {"success": False, "message": "No company data received from API"}

		created_count = 0
		updated_count = 0

		for company in company_data:
			symbol = company.get('symbol')
			if not symbol:
				continue

			# Check if symbol already exists
			if frappe.db.exists("Nepse Symbol", symbol):
				# Update existing symbol
				doc = frappe.get_doc("Nepse Symbol", symbol)
				doc.company_name = company.get('companyName', doc.company_name)
				doc.sector = company.get('sector', doc.sector)
				doc.save()
				updated_count += 1
			else:
				# Create new symbol
				doc = frappe.get_doc({
					"doctype": "Nepse Symbol",
					"symbol": symbol,
					"company_name": company.get('companyName', ''),
					"sector": company.get('sector', ''),
					"status": "Active"
				})
				doc.insert()
				created_count += 1

		return {
			"success": True,
			"message": f"Synced symbols: {created_count} created, {updated_count} updated"
		}

	except Exception as e:
		frappe.log_error(f"Error syncing symbols: {str(e)}")
		return {"success": False, "message": f"Error: {str(e)}"}

@frappe.whitelist()
def update_symbol_prices(symbols=None, date=None):
	"""Update prices for symbols using API with detailed price history data"""
	try:
		if not symbols:
			# Get all active symbols
			symbols = frappe.get_all("Nepse Symbol",
				filters={"status": "Active"},
				pluck="symbol"
			)

		if isinstance(symbols, str):
			symbols = [symbols]

		# Use current date if not provided
		if not date:
			date = frappe.utils.today()

		# Get detailed price data from API using price history function
		price_data = get_prices_history(symbols, date)

		updated_count = 0
		for symbol, data in price_data.items():
			if data and data.get('ltp'):
				try:
					doc = frappe.get_doc("Nepse Symbol", symbol)
					doc.last_traded_price = data['ltp']
					doc.last_updated = frappe.utils.now()
					doc.save()
					updated_count += 1
				except frappe.DoesNotExistError:
					continue

		return {
			"success": True,
			"message": f"Updated prices for {updated_count} symbols"
		}

	except Exception as e:
		frappe.log_error(f"Error updating prices: {str(e)}")
		return {"success": False, "message": f"Error: {str(e)}"}



