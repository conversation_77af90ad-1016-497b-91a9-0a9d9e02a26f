{"actions": [], "allow_rename": 1, "autoname": "naming_series:", "creation": "2025-06-22 10:00:00.000000", "doctype": "DocType", "engine": "InnoDB", "field_order": ["naming_series", "transaction_type", "symbol", "column_break_4", "transaction_date", "status", "section_break_7", "quantity", "price_per_share", "column_break_10", "total_amount", "brokerage", "net_amount", "section_break_14", "broker", "remarks"], "fields": [{"fieldname": "naming_series", "fieldtype": "Select", "label": "Series", "options": "NT-.YYYY.-", "reqd": 1}, {"fieldname": "transaction_type", "fieldtype": "Select", "in_list_view": 1, "label": "Transaction Type", "options": "Buy\nSell", "reqd": 1}, {"fieldname": "symbol", "fieldtype": "Link", "in_list_view": 1, "label": "Symbol", "options": "Nepse Symbol", "reqd": 1}, {"fieldname": "column_break_4", "fieldtype": "Column Break"}, {"fieldname": "transaction_date", "fieldtype": "Date", "in_list_view": 1, "label": "Transaction Date", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "label": "Status", "options": "Draft\nCompleted\nCancelled", "default": "Draft"}, {"fieldname": "section_break_7", "fieldtype": "Section Break", "label": "Transaction Details"}, {"fieldname": "quantity", "fieldtype": "Int", "in_list_view": 1, "label": "Quantity", "reqd": 1}, {"fieldname": "price_per_share", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "label": "Price per Share", "reqd": 1}, {"fieldname": "column_break_10", "fieldtype": "Column Break"}, {"fieldname": "total_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Total Amount", "read_only": 1}, {"fieldname": "brokerage", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Brokerage"}, {"fieldname": "net_amount", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Net Amount", "read_only": 1}, {"fieldname": "section_break_14", "fieldtype": "Section Break", "label": "Additional Information"}, {"fieldname": "broker", "fieldtype": "Data", "label": "Broker"}, {"fieldname": "remarks", "fieldtype": "Text", "label": "Remarks"}], "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-06-22 10:00:00.000000", "modified_by": "Administrator", "module": "Stonks", "name": "Nepse Transaction", "naming_rule": "By Naming Series", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "submit": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Stock Manager", "share": 1, "submit": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}